import React, { useState, useEffect, useRef } from 'react';
import { getCompanyName } from '../utils/companyLookup';

interface MarketIndicator {
  label: string;
  value: string;
  change: string;
  trend: 'up' | 'down' | 'neutral';
  color: string;
}

interface SpeedTrafficProps {
  symbol?: string;
  onPhaseMessage?: (message: string, hasReportButton?: boolean) => void;
  onAnalysisComplete?: (results: AnalysisResults) => void;
  onLightsUpdate?: (lights: {
    technical: 'inactive' | 'red' | 'yellow' | 'green';
    industry: 'inactive' | 'red' | 'yellow' | 'green';
    market: 'inactive' | 'red' | 'yellow' | 'green';
    risk: 'inactive' | 'red' | 'yellow' | 'green';
  }) => void;
}

// LSTM 관련 인터페이스 제거됨

interface MFIResult {
  symbol: string;
  date: string;
  mfi_14: number;
  traffic_light: string;
}

// Define interfaces for API responses
interface Phase1Result {
  mfi?: MFIResult;
  bollinger?: any;
  rsi?: any;
  industry?: any;
  capm?: any;
  garch?: any;
  traffic_lights?: {
    technical?: string;
    industry?: string;
    market?: string;
    risk?: string;
  };
}

interface PredictionResult {
  phase?: number;
  mfi?: MFIResult;
  bollinger?: any;
  rsi?: any;
  industry?: any;
  capm?: any;
  garch?: any;
  traffic_lights?: {
    technical?: string;
    industry?: string;
    market?: string;
    risk?: string;
  };
}

interface AnalysisResults {
  symbol: string;
  companyName: string;
  timestamp: string;
  analysisDate: string;
  mfi?: MFIResult;
  bollinger?: any;
  rsi?: any;
  industry?: any;
  capm?: any;
  garch?: any;
  traffic_lights: {
    technical?: string;
    industry?: string;
    market?: string;
    risk?: string;
  };
}

const SpeedTraffic: React.FC<SpeedTrafficProps> = ({ symbol, onPhaseMessage, onAnalysisComplete, onLightsUpdate }) => {
  // Market indicators state (Pre-ticker mode)
  const [indicators, setIndicators] = useState<MarketIndicator[]>([]);
  const [loading, setLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<string>('');

  // Traffic lights state (4단계 분석)
  const [technicalLight, setTechnicalLight] = useState<'good' | 'warning' | 'danger' | 'inactive'>('inactive'); // Light 1: Technical Analysis
  const [industryLight, setIndustryLight] = useState<'good' | 'warning' | 'danger' | 'inactive'>('inactive'); // Light 2: Industry Sensitivity
  const [marketLight, setMarketLight] = useState<'good' | 'warning' | 'danger' | 'inactive'>('inactive'); // Light 3: Market Sensitivity (CAPM)
  const [riskLight, setRiskLight] = useState<'good' | 'warning' | 'danger' | 'inactive'>('inactive'); // Light 4: Volatility Risk

  // Prediction state
  const [analysisLoading, setAnalysisLoading] = useState(false);
  const [predictionError, setPredictionError] = useState<string | null>(null);
  const [showTimeoutMessage, setShowTimeoutMessage] = useState(false);
  const [lastRequestTime, setLastRequestTime] = useState<number>(0);

  // Results storage state
  const [allResults, setAllResults] = useState<Partial<AnalysisResults>>({});

  // 신호등 상태 변환 함수
  const convertLightStatus = (status: 'good' | 'warning' | 'danger' | 'inactive'): 'inactive' | 'red' | 'yellow' | 'green' => {
    switch (status) {
      case 'good': return 'green';
      case 'warning': return 'yellow';
      case 'danger': return 'red';
      default: return 'inactive';
    }
  };

  // 신호등 상태 변경 시 부모에게 알림
  useEffect(() => {
    if (onLightsUpdate) {
      onLightsUpdate({
        technical: convertLightStatus(technicalLight),
        industry: convertLightStatus(industryLight),
        market: convertLightStatus(marketLight),
        risk: convertLightStatus(riskLight)
      });
    }
  }, [technicalLight, industryLight, marketLight, riskLight, onLightsUpdate]);

  // Single-flight guard
  const inFlight = useRef(false);





  // 한국 시장 데이터 표시 (Pre-ticker mode)
  const fetchMarketData = async () => {
    try {
      setLoading(true);
      // 한국 시장 지표 fallback 데이터 사용
      setIndicators([
        { label: 'KOSPI', value: '2,567.89', change: '+1.2%', trend: 'up', color: 'green' },
        { label: 'KOSDAQ', value: '834.56', change: '+0.8%', trend: 'up', color: 'green' },
        { label: 'KRX 100', value: '1,234.12', change: '-0.3%', trend: 'down', color: 'red' },
        { label: '달러/원', value: '1,327.50', change: '+0.5%', trend: 'up', color: 'green' },
      ]);
      setLastUpdate(new Date().toLocaleTimeString('ko-KR'));
    } catch (error) {
      console.error('Failed to fetch market data:', error);
    } finally {
      setLoading(false);
    }
  };

  // Convert result color to traffic light status
  const resultColorToStatus = (color: string): 'good' | 'warning' | 'danger' | 'inactive' => {
    switch (color?.toLowerCase()) {
      case 'green':
      case 'good':
        return 'good';
      case 'yellow':
      case 'warning':
        return 'warning';
      case 'red':
      case 'danger':
        return 'danger';
      case 'inactive':
      case 'grey':
      case 'gray':
        return 'inactive';
      default:
        return 'warning';
    }
  };



  // 단일 단계 분석 실행 (6개 서비스)
  const fetchAnalysis = async () => {
    if (!symbol || inFlight.current) return;

    // Prevent too frequent requests (minimum 10 seconds between requests)
    const now = Date.now();
    if (now - lastRequestTime < 10000) {
      console.log('Prediction request throttled - too frequent');
      return;
    }

    try {
      // Set single-flight guard
      inFlight.current = true;
      setLastRequestTime(now);

      // Reset all lights to inactive state (4단계)
      setTechnicalLight('inactive');
      setIndustryLight('inactive');
      setMarketLight('inactive');
      setRiskLight('inactive');

      setPredictionError(null);
      setShowTimeoutMessage(false);

      // Get company name for user feedback
      const companyName = getCompanyName(symbol);

      // Initial user feedback message
      onPhaseMessage?.(`🚀 ${companyName} 차트 분석을 시작할게요! 📊`);

      // Wait 1.5 seconds before starting Phase 1
      await new Promise(resolve => setTimeout(resolve, 1500));

      // 6개 분석 서비스 실행
      setAnalysisLoading(true);

      // 새로운 speedtraffic_analysis API 호출
      const analysisResponse = await fetch(`/api/speedtraffic_analysis?symbol=${symbol}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
        signal: AbortSignal.timeout(45000), // 45초 타임아웃
      });

      if (!analysisResponse.ok) {
        throw new Error(`분석 HTTP ${analysisResponse.status}: ${analysisResponse.statusText}`);
      }

      // 분석 결과 파싱
      const analysisResult = await analysisResponse.json() as PredictionResult;

      // 📊 SpeedTraffic 분석 완료 로그
      console.log(`🎯 [SpeedTraffic] ${symbol} 분석 완료 - Traffic Lights:`, analysisResult.traffic_lights);

      // 신호등 업데이트
      if (analysisResult.traffic_lights) {
        if (analysisResult.traffic_lights.technical) {
          setTechnicalLight(resultColorToStatus(analysisResult.traffic_lights.technical));
        }
        if (analysisResult.traffic_lights.industry) {
          setIndustryLight(resultColorToStatus(analysisResult.traffic_lights.industry));
        }
        if (analysisResult.traffic_lights.market) {
          setMarketLight(resultColorToStatus(analysisResult.traffic_lights.market));
        }
        if (analysisResult.traffic_lights.risk) {
          setRiskLight(resultColorToStatus(analysisResult.traffic_lights.risk));
        }
      }

      // 분석 결과 저장
      setAllResults(prev => ({
        ...prev,
        symbol,
        mfi: analysisResult.mfi,
        bollinger: analysisResult.bollinger,
        rsi: analysisResult.rsi,
        industry: analysisResult.industry,
        capm: analysisResult.capm,
        garch: analysisResult.garch,
        traffic_lights: {
          ...prev.traffic_lights,
          ...analysisResult.traffic_lights
        }
      }));

      setAnalysisLoading(false);

      // 분석 완료 메시지
      onPhaseMessage?.('기술적 분석, 산업 민감도, 시장 민감도, 변동성 리스크 분석을 완료했어요! 📊');

      // 분석 완료
      const finalResults: AnalysisResults = {
        symbol,
        companyName: getCompanyName(symbol),
        timestamp: new Date().toISOString(),
        analysisDate: new Date().toISOString().split('T')[0],
        mfi: analysisResult.mfi || allResults.mfi,
        bollinger: analysisResult.bollinger || allResults.bollinger,
        rsi: analysisResult.rsi || allResults.rsi,
        industry: analysisResult.industry || allResults.industry,
        capm: analysisResult.capm || allResults.capm,
        garch: analysisResult.garch || allResults.garch,
        traffic_lights: analysisResult.traffic_lights || {}
      };

      // Store results
      setAllResults(finalResults);

      // 분석 완료 콜백 호출
      onAnalysisComplete?.(finalResults);

      // 완료 메시지 전송
      onPhaseMessage?.('4단계 분석이 완료되었습니다! 투자 신호등을 확인해보세요. 🎯', true);
      console.log(`[SpeedTraffic] 4단계 분석 완료 for ${symbol}`);

    } catch (error) {
      console.error('Staged prediction error:', error);

      if (error instanceof Error) {
        if (error.name === 'TimeoutError') {
          setPredictionError('요청 시간 초과');
        } else {
          setPredictionError(`예측 실패: ${error.message}`);
        }
      } else {
        setPredictionError('예측 서비스 연결 실패');
      }

      // Reset all lights to inactive on error (4단계)
      setTechnicalLight('inactive');
      setIndustryLight('inactive');
      setMarketLight('inactive');
      setRiskLight('inactive');

    } finally {
      setAnalysisLoading(false);
      setShowTimeoutMessage(false);
      inFlight.current = false;
    }
  };

  // Effects
  useEffect(() => {
    if (symbol) {
      // When symbol is provided, fetch analysis once
      fetchAnalysis();
    } else {
      // When no symbol, fetch market data initially
      fetchMarketData();
    }
  }, [symbol]);

  // 20초마다 시장 데이터 업데이트 (Pre-ticker mode only)
  useEffect(() => {
    if (!symbol) {
      const interval = setInterval(() => {
        fetchMarketData();
      }, 20000);

      return () => clearInterval(interval);
    }
  }, [symbol]);

  // Utility functions for rendering
  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return (
          <svg className="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 17l9.2-9.2M17 17V7H7" />
          </svg>
        );
      case 'down':
        return (
          <svg className="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 7l-9.2 9.2M7 7v10h10" />
          </svg>
        );
      default:
        return (
          <svg className="w-4 h-4 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
          </svg>
        );
    }
  };

  const getChangeColor = (trend: string) => {
    switch (trend) {
      case 'up': return 'text-green-600';
      case 'down': return 'text-red-600';
      default: return 'text-slate-500';
    }
  };

  const getTrafficLightColor = (status: 'good' | 'warning' | 'danger') => {
    switch (status) {
      case 'good': return 'bg-green-500';
      case 'warning': return 'bg-yellow-500';
      case 'danger': return 'bg-red-500';
      default: return 'bg-gray-400';
    }
  };

  const getStatusText = (status: 'good' | 'warning' | 'danger' | 'inactive') => {
    switch (status) {
      case 'good': return '양호';
      case 'warning': return '보통';
      case 'danger': return '주의';
      case 'inactive': return '비활성화';
      default: return '분석중';
    }
  };

  // Post-ticker mode: Traffic lights display
  if (symbol) {
    // 4단계 분석 완료 여부 확인
    const allLightsActive = technicalLight !== 'inactive' &&
      industryLight !== 'inactive' &&
      marketLight !== 'inactive' &&
      riskLight !== 'inactive';

    // 분석 완료 상태 표시용 (현재 사용하지 않음)

    return (
      <div className="space-y-4 max-w-full overflow-hidden">
        {/* SpeedTraffic™ 브랜딩 */}
        <div className="text-center mb-4">
          <h2 className="text-2xl font-bold text-transparent bg-gradient-to-r from-blue-400 via-purple-500 to-blue-600 bg-clip-text">
            SpeedTraffic™
          </h2>
        </div>



        {/* 5-Light 신호등 시스템 */}
        <div className="bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl p-7 shadow-xl border border-slate-700">
          <div className="space-y-5">
            {/* Light 1: Technical Analysis */}
            <div>
              <div className="flex items-center justify-between group">
                <div className="flex items-center space-x-5">
                  <div className="relative flex-shrink-0">
                    <div className={`w-6 h-6 rounded-full ${
                      technicalLight === 'inactive' ? 'bg-gray-500' : getTrafficLightColor(technicalLight)
                    } shadow-lg transition-colors duration-300`}></div>
                  </div>
                  <span className={`text-sm font-medium truncate ${
                    technicalLight === 'inactive' ? 'text-gray-400' : 'text-white group-hover:text-blue-300'
                  } transition-colors`}>
                    기술적 분석
                  </span>
                </div>
                <span className={`text-xs px-2 py-0.5 rounded font-medium whitespace-nowrap ${
                  technicalLight === 'inactive' ? 'text-gray-400 bg-gray-800/50' :
                  technicalLight === 'good' ? 'text-green-300 bg-green-900/30' :
                  technicalLight === 'warning' ? 'text-yellow-300 bg-yellow-900/30' :
                  'text-red-300 bg-red-900/30'
                }`}>
                  {technicalLight === 'inactive' ? '분석중' : getStatusText(technicalLight)}
                </span>
              </div>
            </div>

            {/* Light 2: Industry Sensitivity */}
            <div>
              <div className="flex items-center justify-between group">
                <div className="flex items-center space-x-5">
                  <div className="relative flex-shrink-0">
                    <div className={`w-6 h-6 rounded-full ${
                      industryLight === 'inactive' ? 'bg-gray-500' : getTrafficLightColor(industryLight)
                    } shadow-lg transition-colors duration-300`}></div>
                  </div>
                  <span className={`text-sm font-medium ${
                    industryLight === 'inactive' ? 'text-gray-400' : 'text-white group-hover:text-blue-300'
                  } transition-colors`}>
                    산업 민감도
                  </span>
                </div>
                <span className={`text-xs px-2 py-0.5 rounded font-medium whitespace-nowrap ${
                  industryLight === 'inactive' ? 'text-gray-400 bg-gray-800/50' :
                  industryLight === 'good' ? 'text-green-300 bg-green-900/30' :
                  industryLight === 'warning' ? 'text-yellow-300 bg-yellow-900/30' :
                  'text-red-300 bg-red-900/30'
                }`}>
                  {industryLight === 'inactive' ? '분석중' : getStatusText(industryLight)}
                </span>
              </div>
            </div>

            {/* Light 3: Market Sensitivity (CAPM) */}
            <div>
              <div className="flex items-center justify-between group">
                <div className="flex items-center space-x-5">
                  <div className="relative flex-shrink-0">
                    <div className={`w-6 h-6 rounded-full ${
                      marketLight === 'inactive' ? 'bg-gray-500' : getTrafficLightColor(marketLight)
                    } shadow-lg transition-colors duration-300`}></div>
                  </div>
                  <span className={`text-sm font-medium ${
                    marketLight === 'inactive' ? 'text-gray-400' : 'text-white group-hover:text-blue-300'
                  } transition-colors`}>
                    시장 민감도
                  </span>
                </div>
                <span className={`text-xs px-2 py-0.5 rounded font-medium whitespace-nowrap ${
                  marketLight === 'inactive' ? 'text-gray-400 bg-gray-800/50' :
                  marketLight === 'good' ? 'text-green-300 bg-green-900/30' :
                  marketLight === 'warning' ? 'text-yellow-300 bg-yellow-900/30' :
                  'text-red-300 bg-red-900/30'
                }`}>
                  {marketLight === 'inactive' ? '분석중' : getStatusText(marketLight)}
                </span>
              </div>
            </div>

            {/* Light 4: Volatility Risk */}
            <div>
              <div className="flex items-center justify-between group">
                <div className="flex items-center space-x-5">
                  <div className="relative flex-shrink-0">
                    <div className={`w-6 h-6 rounded-full ${
                      riskLight === 'inactive' ? 'bg-gray-500' : getTrafficLightColor(riskLight)
                    } shadow-lg transition-colors duration-300`}></div>
                  </div>
                  <span className={`text-sm font-medium ${
                    riskLight === 'inactive' ? 'text-gray-400' : 'text-white group-hover:text-blue-300'
                  } transition-colors`}>
                    변동성 리스크
                  </span>
                </div>
                <span className={`text-xs px-2 py-0.5 rounded font-medium whitespace-nowrap ${
                  riskLight === 'inactive' ? 'text-gray-400 bg-gray-800/50' :
                  riskLight === 'good' ? 'text-green-300 bg-green-900/30' :
                  riskLight === 'warning' ? 'text-yellow-300 bg-yellow-900/30' :
                  'text-red-300 bg-red-900/30'
                }`}>
                  {riskLight === 'inactive' ? '분석중' : getStatusText(riskLight)}
                </span>
              </div>
            </div>


          </div>
        </div>







        {/* Korean Timeout Message */}
        {showTimeoutMessage && (
          <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-3">
            <div className="text-xs text-yellow-600 text-center">
              이 작업은 시간이 걸립니다... (최대 60초)
            </div>
          </div>
        )}

        {/* Error Display */}
        {predictionError && (
          <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-3">
            <div className="flex items-center space-x-2 text-xs text-red-600">
              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              <span>오류: {predictionError}</span>
            </div>
          </div>
        )}

        {/* Retry Button */}
        {predictionError && !analysisLoading && (
          <div className="text-center">
            <button
              onClick={fetchAnalysis}
              className="px-4 py-2 bg-blue-500 text-white rounded-lg text-sm hover:bg-blue-600 transition-colors"
            >
              다시 시도
            </button>
          </div>
        )}


      </div>
    );
  }

  // Pre-ticker mode: Market indicators display
  return (
    <div className="space-y-4">
      {/* 헤더 */}
      <div className="flex items-center justify-between">
        <h3 className="font-semibold text-slate-900">시장 현황</h3>
        <div className={`w-2 h-2 rounded-full animate-pulse ${loading ? 'bg-yellow-400' : 'bg-green-400'}`}></div>
      </div>

      {/* 지표 목록 */}
      <div className="space-y-3">
        {loading && indicators.length === 0 ? (
          // 로딩 스켈레톤
          Array.from({ length: 5 }).map((_, index) => (
            <div key={index} className="p-3 bg-slate-50 rounded-lg border border-slate-100 animate-pulse">
              <div className="flex items-center justify-between mb-1">
                <div className="h-4 bg-slate-200 rounded w-16"></div>
                <div className="w-4 h-4 bg-slate-200 rounded"></div>
              </div>
              <div className="flex items-center justify-between">
                <div className="h-6 bg-slate-200 rounded w-20"></div>
                <div className="h-4 bg-slate-200 rounded w-12"></div>
              </div>
            </div>
          ))
        ) : (
          indicators.map((indicator, index) => (
          <div
            key={index}
            className="p-3 bg-slate-50 rounded-lg border border-slate-100 hover:bg-slate-100 transition-colors duration-200"
          >
            <div className="flex items-center justify-between mb-1">
              <span className="text-sm font-medium text-slate-700">{indicator.label}</span>
              {getTrendIcon(indicator.trend)}
            </div>
            <div className="flex items-center justify-between">
              <span className="text-lg font-semibold text-slate-900">{indicator.value}</span>
              <span className={`text-sm font-medium ${getChangeColor(indicator.trend)}`}>
                {indicator.change}
              </span>
            </div>
          </div>
          ))
        )}
      </div>

      {/* 푸터 */}
      <div className="pt-3 border-t border-slate-200">
        <div className="flex items-center justify-center space-x-2 text-xs text-slate-500">
          <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>
            {loading ? '업데이트 중...' :
             lastUpdate ? `마지막 업데이트: ${lastUpdate}` :
             '20초마다 업데이트'}
          </span>
        </div>
      </div>
    </div>
  );
};

export default SpeedTraffic;

