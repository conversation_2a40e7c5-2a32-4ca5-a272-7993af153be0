﻿'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import FinancialChart from '@/components/FinancialChart';
import SpeedTraffic from '@/components/SpeedTraffic';
import SpeedTrafficLights from '@/components/SpeedTrafficLights';
import { getCompanyName } from '@/utils/companyLookup';
import { KOSPI_ENRICHED_FINAL } from '@/data/kospi_enriched_final';

// 업종 분석 함수
function analyzeSectorPerformance(symbol: string) {
  const companyData = KOSPI_ENRICHED_FINAL[symbol as keyof typeof KOSPI_ENRICHED_FINAL];
  if (!companyData) {
    return { signal: 'yellow', description: '업종 정보 없음' };
  }

  const targetIndustry = companyData.industry;

  // 같은 업종의 기업들 찾기 (최대 8개)
  const sameIndustryCompanies = Object.entries(KOSPI_ENRICHED_FINAL)
    .filter(([ticker, data]) => data.industry === targetIndustry && ticker !== symbol)
    .slice(0, 8);

  if (sameIndustryCompanies.length === 0) {
    return { signal: 'yellow', description: '업종 비교 데이터 부족' };
  }

  // 간단한 모의 회귀분석 (실제로는 주가 데이터가 필요)
  const hash = symbol.split('').reduce((a, b) => {
    a = ((a << 5) - a) + b.charCodeAt(0);
    return a & a;
  }, 0);

  const sectorScore = Math.abs(hash) % 100;

  if (sectorScore > 70) {
    return { signal: 'green', description: `${targetIndustry} 업종 상위 성과` };
  } else if (sectorScore > 40) {
    return { signal: 'yellow', description: `${targetIndustry} 업종 평균 수준` };
  } else {
    return { signal: 'red', description: `${targetIndustry} 업종 하위 성과` };
  }
}

// KOSPI 지수 분석 함수
function analyzeMarketRisk(symbol: string) {
  // 간단한 모의 KOSPI 지수 분석
  const hash = symbol.split('').reduce((a, b) => {
    a = ((a << 5) - a) + b.charCodeAt(0);
    return a & a;
  }, 0);

  const marketRisk = Math.abs(hash * 3) % 100;

  if (marketRisk > 70) {
    return { signal: 'red', description: 'KOSPI 대비 높은 변동성' };
  } else if (marketRisk > 40) {
    return { signal: 'yellow', description: 'KOSPI 대비 중간 변동성' };
  } else {
    return { signal: 'green', description: 'KOSPI 대비 안정적 흐름' };
  }
}

export default function SpeedTrafficPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [currentSymbol, setCurrentSymbol] = useState<string | undefined>(undefined);
  const [phaseMessage, setPhaseMessage] = useState<string>('');
  const [isAnalysisComplete, setIsAnalysisComplete] = useState(false);
  const [analysisResults, setAnalysisResults] = useState<any>(null);
  const [chatMessages, setChatMessages] = useState<Array<{message: string, isBot: boolean, timestamp: Date}>>([]);

  // 신호등 상태 관리 (4중 분석)
  const [trafficLights, setTrafficLights] = useState({
    technical: 'inactive' as 'inactive' | 'red' | 'yellow' | 'green',
    industry: 'inactive' as 'inactive' | 'red' | 'yellow' | 'green',
    market: 'inactive' as 'inactive' | 'red' | 'yellow' | 'green',
    risk: 'inactive' as 'inactive' | 'red' | 'yellow' | 'green'
  });

  useEffect(() => {
    if (!searchParams) return;

    const symbol = searchParams.get('symbol');
    if (symbol) {
      setCurrentSymbol(symbol);
    }
  }, [searchParams]);

  const handlePhaseMessage = (message: string, hasReportButton?: boolean) => {
    setPhaseMessage(message);
    if (hasReportButton) {
      setIsAnalysisComplete(true);
    }
  };

  // 디버깅용 분석 결과 저장 함수
  const saveDebugAnalysisResults = async (results: any) => {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `${results.symbol}_${timestamp}_analysis.json`;

      const debugData = {
        metadata: {
          symbol: results.symbol,
          companyName: results.companyName,
          timestamp: results.timestamp,
          analysisDate: results.analysisDate,
          savedAt: new Date().toISOString()
        },
        analysisResults: {
          mfi: results.mfi,
          bollinger: results.bollinger,
          rsi: results.rsi,
          industry: results.industry,
          capm: results.capm,
          garch: results.garch,
          traffic_lights: results.traffic_lights
        },
        rawData: results
      };

      // 브라우저에서는 파일 시스템에 직접 저장할 수 없으므로
      // localStorage와 다운로드 기능을 제공
      const debugKey = `speedtraffic_debug_${results.symbol}_${Date.now()}`;
      localStorage.setItem(debugKey, JSON.stringify(debugData, null, 2));

      console.log(`🔍 [DEBUG] 분석 결과 저장됨: ${debugKey}`);

      // 사용자가 다운로드할 수 있도록 링크 생성
      const blob = new Blob([JSON.stringify(debugData, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);

      // 자동 다운로드는 하지 않고, 콘솔에 다운로드 링크 정보만 출력
      console.log(`📁 [DEBUG] 분석 결과 다운로드 가능: ${filename}`);

      // 디버그 데이터를 전역 변수에 저장하여 개발자 도구에서 접근 가능하게 함
      (window as any).lastSpeedTrafficDebug = debugData;

    } catch (error) {
      console.error('디버깅 데이터 저장 실패:', error);
    }
  };

  const handleAnalysisComplete = (results: any) => {
    setIsAnalysisComplete(true);
    setAnalysisResults(results);

    // 신호등 상태 업데이트
    if (results.traffic_lights) {
      setTrafficLights({
        technical: results.traffic_lights.technical || 'inactive',
        industry: results.traffic_lights.industry || 'inactive',
        market: results.traffic_lights.market || 'inactive',
        risk: results.traffic_lights.risk || 'inactive'
      });
    }

    // 분석 결과를 로컬 스토리지에 저장 (추후 AI 요약용)
    const savedResults = {
      ...results,
      savedAt: new Date().toISOString(),
      id: `analysis_${results.symbol}_${Date.now()}`
    };

    try {
      const existingResults = JSON.parse(localStorage.getItem('speedtraffic_results') || '[]');
      existingResults.push(savedResults);
      // 최근 10개만 유지
      if (existingResults.length > 10) {
        existingResults.splice(0, existingResults.length - 10);
      }
      localStorage.setItem('speedtraffic_results', JSON.stringify(existingResults));
      console.log('📊 SpeedTraffic 분석 결과 저장됨:', savedResults.id);
    } catch (error) {
      console.error('분석 결과 저장 실패:', error);
    }

    // 디버깅용 분석 결과 저장
    saveDebugAnalysisResults(results);

    // hcx-002-dash에게 실제 분석 결과 전송
    setTimeout(() => {
      sendToHcxDash(results);
    }, 1000); // 1초 후 전송
  };

  // hcx-002-dash에게 결과 전송 함수
  const sendToHcxDash = async (analysisData: any) => {
    try {
      handleChatMessage('분석 결과를 AI 해설자에게 전송 중...', true);

      // 실제 분석 데이터 추출
      const mfiData = analysisData.mfi || {};
      const bollingerData = analysisData.bollinger || {};
      const rsiData = analysisData.rsi || {};
      const industryData = analysisData.industry || {};
      const capmData = analysisData.capm || {};
      const garchData = analysisData.garch || {};

      // 분석 결과를 상세하게 포맷팅
      const detailedPrompt = `
다음은 SpeedTraffic™ 6중 AI 분석 결과입니다. 각 분석의 실제 수치, 읽는 법, 그리고 불빛이 왜 그 색깔로 나왔는지에 대한 이유를 포함하여 상세하고 전문적인 해설을 제공해주세요.

**분석 대상**: ${analysisData.companyName} (${analysisData.symbol})
**분석 일시**: ${new Date().toLocaleString('ko-KR')}

**📊 상세 분석 결과**:

**1. MFI (Money Flow Index) 분석**:
- 수치: ${mfiData.mfi_14 || 'N/A'}
- 신호등: ${trafficLights.technical}
- 신호: ${mfiData.signal || 'N/A'}
- 요약: ${mfiData.summary_ko || 'N/A'}

**2. RSI (Relative Strength Index) 분석**:
- 신호등: ${trafficLights.technical}
- 신호: ${rsiData.signal || 'N/A'}
- 요약: ${rsiData.summary_ko || 'N/A'}

**3. Bollinger Bands 분석**:
- 신호등: ${trafficLights.technical}
- 신호: ${bollingerData.signal || 'N/A'}
- 요약: ${bollingerData.summary_ko || 'N/A'}

**4. CAPM (시장 베타) 분석**:
- 신호등: ${trafficLights.market}
- 신호: ${capmData.signal || 'N/A'}
- 요약: ${capmData.summary_ko || 'N/A'}

**5. VaR/GARCH (변동성 리스크) 분석**:
- 신호등: ${trafficLights.risk}
- 신호: ${garchData.signal || 'N/A'}
- 요약: ${garchData.summary_ko || 'N/A'}

**6. Industry (업종 회귀) 분석**:
- 신호등: ${trafficLights.industry}
- 신호: ${industryData.signal || 'N/A'}
- 요약: ${industryData.summary_ko || 'N/A'}

**🚦 종합 신호등 결과**:
- 기술적 분석: ${trafficLights.technical} (MFI, RSI, Bollinger 다수결)
- 업종 분석: ${trafficLights.industry} (업종 대비 성과)
- 시장 분석: ${trafficLights.market} (CAPM 베타 분석)
- 리스크 분석: ${trafficLights.risk} (GARCH 변동성 분석)

**요구사항**:
1. 각 분석 항목별로 구체적인 수치와 의미를 설명해주세요
2. 왜 해당 신호등 색깔이 나왔는지 논리적 근거를 제시해주세요
3. 투자자가 이 결과를 어떻게 해석하고 활용해야 하는지 가이드를 제공해주세요
4. 전체적인 투자 방향성에 대한 종합적인 의견을 제시해주세요

**답변 형식을 반드시 지켜주세요**:
- 각 분석별로 명확하게 구분하여 설명
- 구체적인 수치와 근거 포함
- 투자 의사결정에 도움이 되는 실용적인 조언 포함
`;

      // 간단한 메시지 전송을 위한 새로운 API 엔드포인트 생성 필요
      // 임시로 직접 OpenAI 호출 방식 사용
      const response = await fetch('/api/simple_ai_chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: detailedPrompt
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      // AI 해설 결과를 채팅창에 표시
      handleChatMessage('🤖 AI 해설자 분석 결과:', true);
      handleChatMessage(result.response || '해설 생성에 실패했습니다.', true);

    } catch (error) {
      console.error('hcx-002-dash 전송 실패:', error);
      handleChatMessage('AI 해설자 연결에 실패했습니다. 다시 시도해주세요.', true);
    }
  };

  const handleNewAnalysis = () => {
    setCurrentSymbol(undefined);
    setPhaseMessage('');
    setIsAnalysisComplete(false);
    setAnalysisResults(null);
    setChatMessages([]);
    // 신호등 상태 초기화
    setTrafficLights({
      technical: 'inactive',
      industry: 'inactive',
      market: 'inactive',
      risk: 'inactive'
    });
  };

  const handleSymbolSubmit = (symbol: string) => {
    setCurrentSymbol(symbol);
    setPhaseMessage('');
    setIsAnalysisComplete(false);
    setAnalysisResults(null);
    setChatMessages([]);
    // 신호등 상태 초기화
    setTrafficLights({
      technical: 'inactive',
      industry: 'inactive',
      market: 'inactive',
      risk: 'inactive'
    });
  };



  const handleChatMessage = (message: string, isBot: boolean = true) => {
    setChatMessages(prev => [...prev, {
      message,
      isBot,
      timestamp: new Date()
    }]);
  };

  // Mock 분석 로직 제거됨 - 실제 SpeedTraffic 컴포넌트 사용

  // 종목 입력 폼 컴포넌트
  const SymbolInputForm = () => {
    const [inputSymbol, setInputSymbol] = useState('');

    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault();
      if (inputSymbol.trim()) {
        handleSymbolSubmit(inputSymbol.trim().toUpperCase());
      }
    };

    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
        <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-8 w-full max-w-md">
          <div className="text-center mb-8">
            <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-4 shadow-md">
              <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">SpeedTraffic™</h1>
            <p className="text-gray-600">AI 기반 투자 신호등 시스템</p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label htmlFor="symbol" className="block text-sm font-medium text-gray-700 mb-2">
                분석할 종목 티커
              </label>
              <input
                type="text"
                id="symbol"
                value={inputSymbol}
                onChange={(e) => setInputSymbol(e.target.value)}
                placeholder="예: AAPL, TSLA, NVDA"
                className="w-full px-4 py-3 bg-white border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                required
              />
            </div>

            <button
              type="submit"
              className="w-full py-3 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white font-semibold rounded-lg transition-all duration-200 shadow-md hover:shadow-lg"
            >
              분석 시작
            </button>
          </form>

          <div className="mt-6 text-center">
            <button
              onClick={() => router.push('/')}
              className="text-gray-600 hover:text-gray-900 transition-colors text-sm"
            >
              ← 메인으로 돌아가기
            </button>
          </div>
        </div>
      </div>
    );
  };

  // 메인 분석 화면 컴포넌트
  const AnalysisScreen = () => {
    const companyName = getCompanyName(currentSymbol || '');

    return (
      <div className="min-h-screen bg-gray-50">
        {/* 헤더 */}
        <header className="bg-white border-b border-gray-200 sticky top-0 z-50 shadow-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => router.push('/')}
                  className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                  </svg>
                  <span className="text-sm font-medium">메인</span>
                </button>
                <div className="h-6 w-px bg-gray-300"></div>
                <h1 className="text-xl font-bold text-gray-900">
                  SpeedTraffic™
                </h1>
                <div className="text-gray-600 text-sm">
                  {currentSymbol} • {companyName}
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <button
                  onClick={handleNewAnalysis}
                  className="px-4 py-2 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white rounded-lg transition-all duration-200 text-sm font-medium"
                >
                  새 분석
                </button>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-sm text-gray-600">실시간</span>
                </div>
              </div>
            </div>
          </div>
        </header>

        {/* 메인 콘텐츠 */}
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          {/* 상단: 신호등과 채팅창 */}
          <div className="grid grid-cols-1 lg:grid-cols-5 gap-6 mb-6">
            {/* 신호등 영역 (축소) */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-lg font-semibold text-gray-900">SpeedTraffic™</h2>
                  <div className="text-sm text-gray-500">4중 AI 분석</div>
                </div>

                {/* 실제 신호등처럼 디자인 - 어두운 배경, 4x1 레이아웃 */}
                <div className="bg-gray-900 rounded-xl p-6 mx-auto max-w-[120px]">
                  <div className="grid grid-rows-4 gap-4">
                    {[
                      { name: '기술적', status: trafficLights.technical },
                      { name: '업종', status: trafficLights.industry },
                      { name: '시장', status: trafficLights.market },
                      { name: '리스크', status: trafficLights.risk }
                    ].map((light, index) => (
                      <div key={index} className="text-center">
                        <div className={`w-12 h-12 rounded-full mx-auto mb-2 flex items-center justify-center border-2 border-gray-700 ${
                          light.status === 'green' ? 'bg-green-500 shadow-green-500/50 shadow-lg' :
                          light.status === 'yellow' ? 'bg-yellow-500 shadow-yellow-500/50 shadow-lg' :
                          light.status === 'red' ? 'bg-red-500 shadow-red-500/50 shadow-lg' :
                          'bg-gray-600'
                        }`}>
                          {light.status === 'inactive' ? (
                            <div className="w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin"></div>
                          ) : (
                            <div className={`w-8 h-8 rounded-full ${
                              light.status === 'green' ? 'bg-green-400 animate-pulse' :
                              light.status === 'yellow' ? 'bg-yellow-400 animate-pulse' :
                              light.status === 'red' ? 'bg-red-400 animate-pulse' : 'bg-gray-500'
                            }`}></div>
                          )}
                        </div>
                        <div className="text-xs font-medium text-white">{light.name}</div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* 채팅창 영역 (확장) */}
            <div className="lg:col-span-3">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 h-[400px] flex flex-col">
                <div className="px-4 py-3 border-b border-gray-200">
                  <h3 className="text-lg font-semibold text-gray-900">AI 분석 진행상황 및 해설</h3>
                </div>

                <div className="flex-1 overflow-y-auto p-4 space-y-4">
                  {chatMessages.length === 0 ? (
                    <div className="text-sm text-gray-500 text-center py-8">
                      분석을 시작하면 진행상황이 표시됩니다
                    </div>
                  ) : (
                    chatMessages.map((msg, index) => (
                      <div key={index} className="flex items-start space-x-3">
                        <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center flex-shrink-0">
                          <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                          </svg>
                        </div>
                        <div className="flex-1">
                          <div className="bg-gray-100 rounded-lg px-4 py-3">
                            <div className="text-sm text-gray-800 whitespace-pre-wrap leading-relaxed">{msg.message}</div>
                          </div>
                          <div className="text-xs text-gray-500 mt-2">
                            {msg.timestamp.toLocaleTimeString()}
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* 하단: 큰 차트 */}
          {currentSymbol && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900">실시간 차트</h3>
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                      <span className="text-green-600 text-sm font-medium">LIVE</span>
                    </div>
                    <div className="text-gray-600 text-sm">
                      {getCompanyName(currentSymbol)} ({currentSymbol})
                    </div>
                  </div>
                </div>
              </div>

              <div className="p-3">
                <div className="h-[450px] bg-gray-50 rounded-lg overflow-hidden">
                  <FinancialChart symbol={currentSymbol} isExpanded={false} />
                </div>
              </div>
            </div>
          )}

          {/* 실제 SpeedTraffic 컴포넌트 사용 */}
          <SpeedTraffic
            symbol={currentSymbol}
            onPhaseMessage={handleChatMessage}
            onAnalysisComplete={handleAnalysisComplete}
          />
        </main>
      </div>
    );
  };

  // 현재 심볼이 없으면 입력 폼을, 있으면 분석 화면을 표시
  return currentSymbol ? <AnalysisScreen /> : <SymbolInputForm />;
}